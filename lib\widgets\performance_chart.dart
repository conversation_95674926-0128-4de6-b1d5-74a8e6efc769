import 'package:arcane/arcane.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/report_data.dart';

class Performance<PERSON>hart extends StatelessWidget {
  final List<PerformanceData> performanceData;

  const PerformanceChart({super.key, required this.performanceData});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Performa Produk', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sectionsSpace: 2,
                  centerSpaceRadius: 40,
                  sections: List.generate(
                    performanceData.length,
                    (index) => PieChartSectionData(
                      value: performanceData[index].value,
                      title: '${performanceData[index].value.toInt()}%',
                      color: performanceData[index].color,
                      radius: 80,
                      titleStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: Colors.white),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Column(
              children: List.generate(
                performanceData.length,
                (index) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                  child: Row(
                    children: [
                      Container(width: 16, height: 16, color: performanceData[index].color),
                      const SizedBox(width: 8),
                      Text(performanceData[index].category),
                      const Spacer(),
                      Text('${performanceData[index].value.toInt()}%'),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
