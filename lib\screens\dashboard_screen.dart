import 'package:arcane/arcane.dart';

import '../services/report_service.dart';
import '../widgets/performance_chart.dart';
import '../widgets/sales_chart.dart';
import '../widgets/summary_card.dart';

class DashboardScreen extends StatelessWidget {
  final ReportService reportService = ReportService();

  DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final summaryReports = reportService.getSummaryReports();
    final monthlySales = reportService.getMonthlySales();
    final performanceData = reportService.getPerformanceData();

    return ArcaneScreen(
      title: "Dashboard",
      subtitle: "Ringkasan data dan analitik bisnis",
      actions: [
        IconButtonMenu(
          icon: Icons.dots_three_vertical,
          items: [
            MenuButton(
              child: Text("Laporan Penjualan"),
              onPressed: () {
                // Navigate to sales report screen
              },
            ),
            MenuButton(
              child: Text("Inventaris"),
              onPressed: () {
                // Navigate to inventory screen
              },
            ),
            MenuButton(
              child: Text("Pelanggan"),
              onPressed: () {
                // Navigate to customers screen
              },
            ),
            MenuButton(
              child: Text("Pengaturan"),
              onPressed: () {
                // Navigate to settings screen
              },
            ),
            MenuButton(
              child: Text("Keluar"),
              onPressed: () {
                // Implement logout functionality
              },
            ),
          ],
        ),
      ],
      child: Collection(
        children: [
          Section(
            titleText: "Ringkasan",
            child: SGridView.builder(
              builder: (context, index) {
                final data = summaryReports[index];
                return SummaryCard(data: data);
              },
              childCount: summaryReports.length,
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.5,
            ),
          ),
          Section(
            titleText: "Grafik Penjualan",
            child: SalesChart(salesData: monthlySales),
          ),
          Section(
            titleText: "Performa Produk",
            child: PerformanceChart(performanceData: performanceData),
          ),
        ],
      ),
    );
  }
}
