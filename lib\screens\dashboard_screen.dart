import 'package:arcane/arcane.dart';
import 'package:flutter/material.dart' as material;

import '../services/report_service.dart';
import '../widgets/performance_chart.dart';
import '../widgets/sales_chart.dart';
import '../widgets/summary_card.dart';

class DashboardScreen extends StatelessWidget {
  final ReportService reportService = ReportService();

  DashboardScreen({super.key});

  NavigationBarItem buildButton(String label, IconData icon) {
    return NavigationItem(label: Text(label), child: Icon(icon));
  }

  Widget _buildNavigationSidebar() {
    return NavigationSidebar(
      children: [
        NavigationItem(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Avatar(initials: Avatar.getInitials('Admin User'), size: 64, backgroundColor: Colors.blue, borderRadius: 100),
              ),
              SizedBox(height: 8),
              Text('Admin User', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              Text('<EMAIL>', style: TextStyle(color: Colors.gray)),
            ],
          ),
        ),
        buildButton('Dashboard', BootstrapIcons.house),
        NavigationDivider(),
        NavigationLabel(child: Text('Master Data')),
        buildButton('Truk Comp', BootstrapIcons.box),
        buildButton('Shipper', BootstrapIcons.),
        buildButton('Agent', BootstrapIcons.box),
        buildButton('Shipping Comp', BootstrapIcons.box),
        buildButton('Anak Comp', BootstrapIcons.box),
        buildButton('Consignee', BootstrapIcons.box),
        buildButton('Kota Kirim', BootstrapIcons.box),
        buildButton('Salesman', BootstrapIcons.box),
        buildButton('Container', BootstrapIcons.box),
        buildButton('Lock Tarif', BootstrapIcons.box),
        NavigationDivider(),
        buildButton('Logout', BootstrapIcons.boxArrowLeft),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final summaryReports = reportService.getSummaryReports();
    final monthlySales = reportService.getMonthlySales();
    final performanceData = reportService.getPerformanceData();

    // Check if we're on mobile/tablet for responsive layout
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;
    final isTablet = screenWidth >= 768 && screenWidth < 1024;

    if (isMobile) {
      // Mobile layout with drawer
      return material.Scaffold(
        drawer: material.Drawer(child: _buildNavigationSidebar()),
        body: ArcaneScreen(
          title: "Dashboard",
          subtitle: "Ringkasan data dan analitik bisnis",
          actions: [
            material.Builder(
              builder: (context) => material.IconButton(icon: Icon(Icons.list), onPressed: () => material.Scaffold.of(context).openDrawer()),
            ),
          ],
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text("Ringkasan", style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                SizedBox(height: 16),
                GridView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 2, crossAxisSpacing: 12, mainAxisSpacing: 12, childAspectRatio: 1.5),
                  itemCount: summaryReports.length,
                  itemBuilder: (context, index) {
                    final data = summaryReports[index];
                    return SummaryCard(data: data);
                  },
                ),
                SizedBox(height: 24),
                SalesChart(salesData: monthlySales),
                SizedBox(height: 24),
                PerformanceChart(performanceData: performanceData),
              ],
            ),
          ),
        ),
      );
    } else if (isTablet) {
      // Tablet layout with persistent sidebar
      return Row(
        children: [
          _buildNavigationSidebar(),
          Expanded(
            child: ArcaneScreen(
              title: "Dashboard",
              subtitle: "Ringkasan data dan analitik bisnis",
              child: SingleChildScrollView(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("Ringkasan", style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                    SizedBox(height: 16),
                    GridView.builder(
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 3, crossAxisSpacing: 12, mainAxisSpacing: 12, childAspectRatio: 1.5),
                      itemCount: summaryReports.length,
                      itemBuilder: (context, index) {
                        final data = summaryReports[index];
                        return SummaryCard(data: data);
                      },
                    ),
                    SizedBox(height: 24),
                    SalesChart(salesData: monthlySales),
                    SizedBox(height: 24),
                    PerformanceChart(performanceData: performanceData),
                  ],
                ),
              ),
            ),
          ),
        ],
      );
    } else {
      // Desktop layout with persistent sidebar
      return Row(
        children: [
          _buildNavigationSidebar(),
          Expanded(
            child: ArcaneScreen(
              title: "Dashboard",
              subtitle: "Ringkasan data dan analitik bisnis",
              child: SingleChildScrollView(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("Ringkasan", style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                    SizedBox(height: 16),
                    GridView.builder(
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 4, crossAxisSpacing: 12, mainAxisSpacing: 12, childAspectRatio: 1.5),
                      itemCount: summaryReports.length,
                      itemBuilder: (context, index) {
                        final data = summaryReports[index];
                        return SummaryCard(data: data);
                      },
                    ),
                    SizedBox(height: 24),
                    SalesChart(salesData: monthlySales),
                    SizedBox(height: 24),
                    PerformanceChart(performanceData: performanceData),
                  ],
                ),
              ),
            ),
          ),
        ],
      );
    }
  }
}
