import 'package:flutter/material.dart';

import '../services/report_service.dart';
import '../widgets/performance_chart.dart';
import '../widgets/sales_chart.dart';
import '../widgets/summary_card.dart';

class DashboardScreen extends StatelessWidget {
  final ReportService reportService = ReportService();

  DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final summaryReports = reportService.getSummaryReports();
    final monthlySales = reportService.getMonthlySales();
    final performanceData = reportService.getPerformanceData();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Ringkasan',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 1.5,
              ),
              itemCount: summaryReports.length,
              itemBuilder: (context, index) {
                return SummaryCard(data: summaryReports[index]);
              },
            ),
            const SizedBox(height: 24),
            SalesChart(salesData: monthlySales),
            const SizedBox(height: 24),
            PerformanceChart(performanceData: performanceData),
          ],
        ),
      ),
      drawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            DrawerHeader(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.inversePrimary,
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: Colors.white,
                    child: Icon(Icons.person, size: 40, color: Colors.grey),
                  ),
                  SizedBox(height: 10),
                  Text(
                    'Admin User',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  Text('<EMAIL>'),
                ],
              ),
            ),
            ListTile(
              leading: const Icon(Icons.dashboard),
              title: const Text('Dashboard'),
              selected: true,
              onTap: () {
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.bar_chart),
              title: const Text('Laporan Penjualan'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to sales report screen
              },
            ),
            ListTile(
              leading: const Icon(Icons.inventory),
              title: const Text('Inventaris'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to inventory screen
              },
            ),
            ListTile(
              leading: const Icon(Icons.people),
              title: const Text('Pelanggan'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to customers screen
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('Pengaturan'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to settings screen
              },
            ),
            ListTile(
              leading: const Icon(Icons.logout),
              title: const Text('Keluar'),
              onTap: () {
                Navigator.pop(context);
                // Implement logout functionality
              },
            ),
          ],
        ),
      ),
    );
  }
}