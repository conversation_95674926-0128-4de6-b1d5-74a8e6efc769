import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/report_data.dart';

class SummaryCard extends StatelessWidget {
  final ReportData data;
  final NumberFormat formatter = NumberFormat('#,###');

  SummaryCard({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(data.icon, color: data.color, size: 24),
                const SizedBox(width: 8),
                Text(
                  data.title,
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              formatter.format(data.value),
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: data.color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}