import 'package:arcane/arcane.dart';
import 'package:intl/intl.dart';
import '../models/report_data.dart';

class SummaryCard extends StatelessWidget {
  final ReportData data;
  final NumberFormat formatter = NumberFormat('#,###');

  SummaryCard({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return BasicCard(
      title: Row(
        children: [
          Icon(data.icon, color: data.color, size: 24),
          const SizedBox(width: 8),
          Text(data.title, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
        ],
      ),
      subtitle: Text(
        formatter.format(data.value),
        style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: data.color),
      ),
    );
  }
}
