import 'package:arcane/arcane.dart';
import '../models/report_data.dart';

class ReportService {
  // Simulasi data untuk dashboard
  List<ReportData> getSummaryReports() {
    return [
      ReportData(title: 'Pendapatan', value: 24500000, icon: Icons.currency_dollar, color: Colors.green),
      ReportData(title: 'Pengeluaran', value: 18700000, icon: Icons.shopping_cart, color: Colors.red),
      ReportData(title: 'Profit', value: 5800000, icon: Icons.trend_up, color: Colors.blue),
      ReportData(title: 'Pelanggan', value: 1250, icon: Icons.users, color: Colors.orange),
    ];
  }

  List<SalesData> getMonthlySales() {
    return [
      SalesData(DateTime(2023, 1), 3500000),
      SalesData(DateTime(2023, 2), 5200000),
      SalesData(DateTime(2023, 3), 4000000),
      SalesData(DateTime(2023, 4), 6500000),
      SalesData(DateTime(2023, 5), 5800000),
      SalesData(DateTime(2023, 6), 7200000),
    ];
  }

  List<PerformanceData> getPerformanceData() {
    return [
      PerformanceData('Produk A', 35, Colors.blue),
      PerformanceData('Produk B', 25, Colors.green),
      PerformanceData('Produk C', 20, Colors.orange),
      PerformanceData('Produk D', 15, Colors.purple),
      PerformanceData('Lainnya', 5, Colors.gray),
    ];
  }
}
