import 'package:arcane/arcane.dart';

class ReportData {
  final String title;
  final double value;
  final IconData icon;
  final Color color;

  ReportData({required this.title, required this.value, required this.icon, required this.color});
}

class SalesData {
  final DateTime date;
  final double amount;

  SalesData(this.date, this.amount);
}

class PerformanceData {
  final String category;
  final double value;
  final Color color;

  PerformanceData(this.category, this.value, this.color);
}
